<!DOCTYPE html>
<html lang="en">
<?php
        // Get session instance
        $session = $this->request->getSession();
        $currentLang = $session->read('siteSettings.language') ? strtolower($session->read('siteSettings.language')) : 'english';
        $country = $session->read('siteSettings.country') ? strtolower($session->read('siteSettings.country')) : 'Qatar';
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?= $this->Html->meta('csrfToken', $this->request->getAttribute('csrfToken')); ?>
    <title><?= $session->read('siteSettings.site_title'); ?></title>
    <link rel="stylesheet" href="../../bundles/bootstrap/css/bootstrap.min.css" />
    <link rel="stylesheet" href="../../bundles/bootstrap/css/bootstrap.css" />
    <link rel="stylesheet" href="../../css/ozone.css" />
    <link rel="stylesheet" href="../../css/responsive.css" />
    <!-- For SVG format -->
    <link rel="icon" type="image/svg+xml" href="../../img/ozone/Ozonex-svg.svg">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Owl Stylesheets -->
    <link rel="stylesheet" href="../../css/owl.carousel.min.css">
    <link rel="stylesheet" href="../../css/owl.theme.default.min.css">
    <link href="https://cdn.jsdelivr.net/npm/nouislider@15.7.0/dist/nouislider.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <script src="../../js/jquery.min.js"></script>
    <script src="../../js/owl.carousel.js"></script>
    <style>
        .address_btn{
            color:#004225;
        }
    </style>
</head>
<body>
  <!-- Leading Company Section -->
    <section class="myaccount">
        <div class="container">
            <div class="row py-5">
                <!-- Sidebar Navigation -->
                <div class="col-md-3 mb-4">
                    <div class="list-group">
                        <a class="list-group-item list-group-item-action active" data-bs-toggle="list" href="#profile">My Profile</a>
                        <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#billing-tab">My Address</a>
                        <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#orders-tab">My Order</a>
                        <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#wishlist-tab">My Wishlist</a>
                        <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#changePassword">Change Password</a>
                        <a class="list-group-item list-group-item-action" href="<?= $this->Url->build(['controller' => 'Customer', 'action' => 'logout']) ?>">Logout</a>
                    </div>
                </div>

                <!-- Tab Content -->
                <div class="col-md-9">
                    <div class="tab-content">
                        <!-- Profile Tab -->
                        <div class="tab-pane fade show active" id="profile">
                            <div class="container ">
                                <h4>My Profile</h4>
                                <hr />

                                <!-- Profile Form -->
                                    <div class="mb-3 text-center position-relative">
                                        <label for="profilePic" style="cursor: pointer;">
                                            <img id="profileImagePreview" src="/<?= $users->customer->profile_photo ?? '../assets/dp.png' ?>"
                                                alt="Profile Picture" class="rounded-circle mb-3" width="150" height="150">
                                        </label>
                                    </div>       

                                    <form class="account-form" id="my_profile_form" method="post" action="<?= $this->Url->build(['controller' => 'Account','action' => 'updateMyAccount']); ?>" enctype="multipart/form-data">
                                    <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>
                                    
                                    <div class="row">
                                        <!-- Name -->
                                        <div class="col-md-6 mb-3">
                                            <label for="name" class="form-label">Name</label>
                                            <input type="text" class="form-control" id="name" name="name" value="<?= $users->first_name.' '.$users->last_name ?>" placeholder="Enter your name">
                                        </div>

                                        <!-- Email -->
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">Email address</label>
                                            <input type="email" class="form-control" id="email" name="email" value="<?= $users->email ?>" placeholder="Enter your email">
                                        </div>

                                        <!-- Phone -->
                                        <div class="col-md-6 mb-3">
                                            <label for="phone" class="form-label">Phone</label>
                                            <input type="tel" class="form-control" id="phone" name="phone" value="<?= $users->mobile_no ?>" placeholder="Enter your phone number">
                                        </div>

                                        <!-- Date of Birth -->
                                        <div class="col-md-6 mb-3">
                                            <label for="dob" class="form-label">Date of Birth</label>
                                            <input type="date" class="form-control" id="dob" name="dob" value="<?= isset($users->customer->date_of_birth) ? date("Y-m-d", strtotime($users->customer->date_of_birth)) : '' ?>">
                                        </div>

                                        <!-- Gender -->
                                        <div class="col-md-6 mb-3">
                                            <label for="gender" class="form-label">Gender</label>
                                            <select class="form-select" id="gender" name="gender">
                                                <option value="">Select your gender</option>
                                                <option value="M" <?= isset($users->customer->gender) && $users->customer->gender === 'M' ? 'selected' : '' ?>>Male</option>
                                                <option value="F" <?= isset($users->customer->gender) && $users->customer->gender === 'F' ? 'selected' : '' ?>>Female</option>
                                                <option value="O" <?= isset($users->customer->gender) && $users->customer->gender === 'O' ? 'selected' : '' ?>>Other</option>
                                                <option value="P" <?= isset($users->customer->gender) && $users->customer->gender === 'P' ? 'selected' : '' ?>>Prefer not to say</option>
                                            </select>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="profile_photo" class="form-label">Profile Photo</label>
                                            <input class="form-control" type="file" id="profile_photo" name="profile_photo" accept="image/*"
                                            onchange="previewProfileImage(event)">
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">Email (Login)</label>
                                            <input type="email" class="form-control" id="email" value="<?= $users->email ?>"
                                                readonly>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary">Save Changes</button>
                                </form>
                            </div>
                        </div>

                        <!-- Add Address Modal -->
                            <div class="modal fade" id="addAddressModal" tabindex="-1" aria-labelledby="addAddressModalLabel"
                                aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <form class="modal-content" method="post" action="<?= $this->Url->build(['controller' => 'Account','action' => 'addAddress']); ?>" enctype="multipart/form-data">
                                    <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>

                                        <div class="modal-header">
                                            <h5 class="modal-title">Add New Address</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row">
                                                
                                                <div class="col-md-6 mb-3">
                                                    <label for="address_type" class="form-label">Address Type</label>
                                                    <select class="form-select" id="address_type" name="address_type" required>
                                                        <option value="">Select Address Type</option>
                                                        <option value="Home">Home</option>
                                                        <option value="Work">Work</option>
                                                    </select>
                                                </div>

                                                <div class="col-6 mb-3">
                                                    <label for="name" class="form-label">Name</label>
                                                    <input type="text" class="form-control" id="name" name="name" required>
                                                </div>

                                                <div class="col-6 mb-3">
                                                    <label for="house_no" class="form-label">House No</label>
                                                    <input type="text" class="form-control" id="house_no" name="house_no" required>
                                                </div>

                                                <!-- Address 1 -->
                                                <div class="col-6 mb-3">
                                                    <label for="address1" class="form-label">Address 1</label>
                                                    <input type="text" class="form-control" id="address1" name="address1" required>
                                                </div>

                                                <!-- Address 2 -->
                                                <div class="col-6 mb-4">
                                                    <label for="address2" class="form-label">Address 2</label>
                                                    <input type="text" class="form-control" id="address2" name="address2" required>
                                                </div>

                                                <div class="col-6 mb-3">
                                                    <label for="landmark" class="form-label">Landmark</label>
                                                    <input type="text" class="form-control" id="landmark" name="landmark" required>
                                                </div>

                                                <!-- City -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="city" class="form-label">City</label>
                                                    <select class="form-select" id="city" name="city" required>
                                                        <option value="">Select your City</option>
                                                        <?php foreach ($cities as $id => $name): ?>
                                                            <option value="<?= h($id) ?>" <?= (!empty($users->customer->city_id) && $users->customer->city_id == $id) ? 'selected' : '' ?>>
                                                                <?= h($name) ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                                
                                                <!-- State -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="state" class="form-label">State</label>
                                                    <select class="form-select" id="state" name="state" required>
                                                        <option value="">Select your State</option>
                                                        <?php foreach ($states as $id => $name): ?>
                                                            <option value="<?= h($id) ?>" <?= (!empty($users->customer->state_id) && $users->customer->state_id == $id) ? 'selected' : '' ?>>
                                                                <?= h($name) ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>

                                                <!-- Zip Code -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="zip" class="form-label">Zip Code</label>
                                                    <input type="text" class="form-control" id="zip" name="zip" required>
                                                </div>

                                                <!-- Country -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="country" class="form-label">Country</label>
                                                    <select class="form-select" id="country" name="country" required>
                                                        <option value="">Select your Country</option>
                                                        <?php foreach ($countries as $id => $name): ?>
                                                            <option value="<?= h($id) ?>" <?= (!empty($users->customer->country_id) && $users->customer->country_id == $id) ? 'selected' : '' ?>>
                                                                <?= h($name) ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="submit" class="btn btn-primary">Submit</button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                        <!-- Edit Address Modal -->
                            <div class="modal fade" id="editAddressModal" tabindex="-1" aria-labelledby="editAddressModalLabel"
                                aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <form class="modal-content" method="post" action="<?= $this->Url->build(['controller' => 'Account','action' => 'editAddress']); ?>" enctype="multipart/form-data">
                                    <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>
                                    
                                        <div class="modal-header">
                                            <h5 class="modal-title">Edit Address</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row">
                                                <input type="hidden" id="address_id" name="address_id" value="">
                                                <div class="col-md-6 mb-3">
                                                    <label for="address_type" class="form-label">Address Type</label>
                                                    <select class="form-select" id="address_type" name="address_type">
                                                        <option value="">Select Address Type</option>
                                                        <option value="Home">Home</option>
                                                        <option value="Work">Work</option>
                                                    </select>
                                                </div>

                                                <div class="col-6 mb-3">
                                                    <label for="name" class="form-label">Name</label>
                                                    <input type="text" class="form-control" id="name" name="name" required>
                                                </div>
                                                
                                                <div class="col-6 mb-3">
                                                    <label for="house_no" class="form-label">House No</label>
                                                    <input type="text" class="form-control" id="house_no" name="house_no" required>
                                                </div>

                                                <!-- Address 1 -->
                                                <div class="col-6 mb-3">
                                                    <label for="address1" class="form-label">Address 1</label>
                                                    <input type="text" class="form-control" id="address1" name="address1">
                                                </div>

                                                <!-- Address 2 -->
                                                <div class="col-6 mb-4">
                                                    <label for="address2" class="form-label">Address 2</label>
                                                    <input type="text" class="form-control" id="address2" name="address2">
                                                </div>
                                                
                                                <div class="col-6 mb-3">
                                                    <label for="landmark" class="form-label">Landmark</label>
                                                    <input type="text" class="form-control" id="landmark" name="landmark">
                                                </div>

                                                <!-- City -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="city" class="form-label">City</label>
                                                    <select class="form-select" id="city" name="city">
                                                        <option value="">Select your City</option>
                                                        <?php foreach ($cities as $id => $name): ?>
                                                            <option value="<?= h($id) ?>" <?= (!empty($users->customer->city_id) && $users->customer->city_id == $id) ? 'selected' : '' ?>>
                                                                <?= h($name) ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                                
                                                <!-- State -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="state" class="form-label">State</label>
                                                    <select class="form-select" id="state" name="state">
                                                        <option value="">Select your State</option>
                                                        <?php foreach ($states as $id => $name): ?>
                                                            <option value="<?= h($id) ?>" <?= (!empty($users->customer->state_id) && $users->customer->state_id == $id) ? 'selected' : '' ?>>
                                                                <?= h($name) ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>

                                                <!-- Zip Code -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="zip" class="form-label">Zip Code</label>
                                                    <input type="text" class="form-control" id="zip" name="zip">
                                                </div>

                                                <!-- Country -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="country" class="form-label">Country</label>
                                                    <select class="form-select" id="country" name="country">
                                                        <option value="">Select your Country</option>
                                                        <?php foreach ($countries as $id => $name): ?>
                                                            <option value="<?= h($id) ?>" <?= (!empty($users->customer->country_id) && $users->customer->country_id == $id) ? 'selected' : '' ?>>
                                                                <?= h($name) ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="submit" class="btn btn-primary">Submit</button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                        <!-- Delete Address Modal -->
                            <div class="modal fade" id="deleteAddressModal" tabindex="-1" aria-labelledby="deleteAddressModalLabel"
                                aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <form class="modal-content" method="post" action="<?= $this->Url->build(['controller' => 'Account','action' => 'deleteAddress']); ?>" enctype="multipart/form-data">
                                    <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>
                                    
                                        <div class="modal-header">
                                            <h5 class="modal-title">Delete Address</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Are you sure you want to delete this address? This action cannot be undone.</p>
                                            <input type="hidden" id="address_id" name="address_id" value="">
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                            <button type="submit" class="btn btn-danger">Submit</button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                        <!-- Billing Information Tab -->
                        <div class="tab-pane fade" id="billing-tab">
                            <div class="container ">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h4>My Address</h4>
                                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAddressModal"><i class="fas fa-plus"></i> Add Address</button>
                                </div>

                                <hr />

                                <div class="row">

                                    <?php if (!empty($customerAddresses)): ?>
                                        <?php foreach ($customerAddresses as $k => $address): ?>
                                        <div class="col-md-4 mb-3">
                                            <div class="card">
                                                <div class="card-header justify-content-between align-items-center d-flex">
                                                    <small><?= $address->type ?></small>
                                                    <div class="float-end">
                                                        <input type="hidden" id="csrf-token-address" value="<?= h($this->request->getAttribute('csrfToken')) ?>">
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <h5 class="card-title"><?= $address->name ?></h5>
                                                    <small><?= $address->house_no . ' ' . $address->address_line1 ?></small><br>
                                                    <small><?= $address->address_line2 . ' ' . $address->landmark ?></small><br>
                                                    <small><?= $address->city->city_name . ' ' . $address->state->state_name . ' ' . $address->zipcode ?></small><br>
                                                    <small><?= $address->country->name ?></small><br><br>

                                                    <a class="btn btn-link text-decoration-none" onclick="editAddress(<?= $address->id ?>)" title="Edit">Edit</a>|
                                                    <a class="btn btn-link text-decoration-none" onclick="deleteAddress(<?= $address->id ?>)" title="Delete">Remove</a>
                                                </div>
                                            </div>
                                        </div>

                                        <?php endforeach; ?>
                                        <?php else: ?>
                                            <div class="alert alert-info">No orders found.</div>
                                    <?php endif; ?>     

                                </div>
                            </div>
                        </div>

                        <!-- Orders Tab -->
                        <div class="tab-pane fade" id="orders-tab">
                            <div class="container ">
                                <h4>My Orders</h4>
                                <hr />

                                <!-- Order List -->
                                <div class="accordion" id="orderAccordion">
                                    <?php if (!empty($orders)): ?>
                                        <?php foreach ($orders as $k => $order): ?>
                                            <div class="accordion-item mb-3">
                                                <h2 class="accordion-header" id="headingOrder<?= $order->id ?>">
                                                    <button class="accordion-button <?= $k === 0 ? '' : 'collapsed' ?>" type="button" data-bs-toggle="collapse"
                                                        data-bs-target="#collapseOrder<?= $order->id ?>" aria-expanded="<?= $k === 0 ? 'true' : 'false' ?>" aria-controls="collapseOrder<?= $order->id ?>">
                                                        Order #<?= h($order->order_number ?? $order->id) ?> &nbsp;|&nbsp;
                                                        <span class="status-badge status-<?= strtolower($order->status) ?> ms-2"><?= h($order->status) ?></span>
                                                    </button>
                                                </h2>
                                                <div id="collapseOrder<?= $order->id ?>" class="accordion-collapse collapse <?= $k === 0 ? 'show' : '' ?>"
                                                    aria-labelledby="headingOrder<?= $order->id ?>" data-bs-parent="#orderAccordion">
                                                    <div class="accordion-body">
                                                        <div class="row mb-3">
                                                            <div class="col-md-6">
                                                                <p><strong>Order Date:</strong> <?= h($order->order_date ? date('Y-m-d', strtotime($order->order_date)) : '') ?></p>
                                                                <p><strong>Estimated Delivery:</strong> <?= h($order->delivery_date ?? '-') ?></p>
                                                            </div>
                                                        </div>
                                                        <?php if (!empty($order->order_items)): ?>
                                                            <ul class="list-group mb-3">
                                                                <?php foreach ($order->order_items as $item): ?>
                                                                    <li class="list-group-item">
                                                                        <div class="d-flex justify-content-between">
                                                                            <div>
                                                                                <strong><?= h($item->product->name ?? 'Product') ?></strong> <br>
                                                                                Quantity: <?= h($item->quantity) ?>
                                                                            </div>
                                                                            <div><strong><?= number_format($item->price, 2) ?></strong></div>
                                                                        </div>
                                                                    </li>
                                                                <?php endforeach; ?>
                                                            </ul>
                                                        <?php endif; ?>
                                                        <!-- Action Buttons -->
                                                        <div class="d-flex flex-wrap gap-2">
                                                            <button class="btn btn-outline-danger btn-sm" data-bs-toggle="modal"
                                                                data-bs-target="#cancelModal" data-order-id="<?= $order->id ?>">Cancel Order</button>
                                                            <button class="btn btn-primary btn-sm" data-bs-toggle="modal"
                                                                data-bs-target="#returnModal" data-order-id="<?= $order->id ?>">Request Return</button>
                                                            <a href="<?= $this->Url->build(['controller' => 'Account','action' => 'downloadInvoice', $order->id]); ?>" class="btn btn-outline-primary btn-sm">Download Invoice (PDF)</a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="alert alert-info">No orders found.</div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Cancel Modal -->
                            <div class="modal fade" id="cancelModal" tabindex="-1" aria-labelledby="cancelModalLabel"
                                aria-hidden="true">
                                <div class="modal-dialog">
                                    <form class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Cancel Order</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            Are you sure you want to cancel this order? This can only be done before
                                            shipment.
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary"
                                                data-bs-dismiss="modal">No</button>
                                            <button type="submit" class="btn btn-danger">Yes, Cancel</button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Return Modal -->
                            <div class="modal fade" id="returnModal" tabindex="-1" aria-labelledby="returnModalLabel"
                                aria-hidden="true">
                                <div class="modal-dialog">
                                    <form class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Request Return/Refund</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Request a return for this order. Please note that all returns must be
                                                within the allowed return period and are subject to admin approval.</p>
                                            <div class="mb-3">
                                                <label for="returnReason" class="form-label">Reason for return</label>
                                                <textarea class="form-control" id="returnReason" rows="3"
                                                    required></textarea>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary"
                                                data-bs-dismiss="modal">Close</button>
                                            <button type="submit" class="btn btn-warning">Submit Request</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            
                        </div>
                        
                        <!-- wishlist Tab -->
                        <div class="tab-pane fade" id="wishlist-tab">
                            <div class="container">
                                <h4 class="mb-4">My Wishlist</h4>

                                <!-- Wishlist Grid -->
                                <div class="row row-cols-1 row-cols-md-3 g-4">
                                    <?php if (!empty($wishlistItems)): ?>
                                        <?php foreach ($wishlistItems as $wishlist): ?>
                                            <div class="col">
                                                <div class="card wishlist-card h-100">
                                                    <img src="<?= h($wishlist->product->product_image ?? '../../img/ozone/Meryl_Lounge-view.png') ?>" class="card-img-top"
                                                        alt="Product Image">
                                                    <div class="card-body d-flex flex-column">
                                                        <h5 class="card-title"><?= h($wishlist->product->name ?? 'Product Title') ?></h5>
                                                        <p class="card-text"><strong>Price:</strong> <?= isset($wishlist->product->promotion_price) ? '$' . number_format($wishlist->product->promotion_price, 2) : '' ?></p>
                                                        <p class="card-text"><strong>Added by:</strong> <?= h($wishlist->product->added_by ?? 'Admin Name') ?></p>
                                                        <input type="hidden" id="csrf-token" value="<?= h($this->request->getAttribute('csrfToken')) ?>">
                                                        <div class="mt-auto">
                                                            <button class="btn btn-primary btn-sm w-100 mb-2" onclick="add_to_cart(<?= $wishlist->product->id ?>,<?= $users->customer->id ?>)">Add to Cart</button>
                                                            <button class="btn btn-outline-danger btn-sm w-100" onclick="remove_from_wishlist(<?= $wishlist->product->id ?>,<?= $users->customer->id ?>)">Remove</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="col">
                                            <div class="alert alert-info w-100">No wishlist items found.</div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Change Password Tab -->
                        <div class="tab-pane fade" id="changePassword">
                            <h4>Change Password</h4>
                            <hr />
                            <form class="account-form" id="my_profile_form" method="post" action="<?= $this->Url->build(['controller' => 'Account','action' => 'changePassword']); ?>" enctype="multipart/form-data">
                                <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>

                                <div class="mb-3">
                                    <label for="currentPassword" class="form-label">Current Password</label>
                                    <input type="password" class="form-control" id="currentPassword" name="currentPassword"
                                        placeholder="Enter current password" required>
                                </div>
                                <div class="mb-3">
                                    <label for="newPassword" class="form-label">New Password</label>
                                    <input type="password" class="form-control" id="newPassword" name="newPassword"
                                        placeholder="Enter new password" required>
                                </div>
                                <div class="mb-3">
                                    <label for="confirmPassword" class="form-label">Confirm New Password</label>
                                    <input type="password" class="form-control" id="confirmPassword" name="confirmPassword"
                                        placeholder="Confirm new password" required>
                                </div>
                                <button type="submit" class="btn btn-primary">Update Password</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
<script>
    document.addEventListener("DOMContentLoaded", function () {
        const hash = window.location.hash;
        if (hash) {
            setTimeout(function () {
                const triggerEl = document.querySelector(`a[href="${hash}"]`);
                if (triggerEl) {
                    const tab = new bootstrap.Tab(triggerEl);
                    tab.show();
                }
            }, 200);
        }
    });

    function deleteAddress(address_id) {
        $('#deleteAddressModal').find('#address_id').val(address_id);
        $('#deleteAddressModal').modal('show');
    }

    function editAddress(address_id) {
        const csrfToken = $('#csrf-token-address').val();
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'getAddressById']) ?>',
            type: 'POST',
            data: { address_id: address_id, _csrfToken: csrfToken },
            success: function(response) {
                if (response.success) {
                    $('#editAddressModal').find('#address_id').val(response.address.id);
                    $('#editAddressModal').find('#name').val(response.address.name);
                    $('#editAddressModal').find('#address_type').val(response.address.type);
                    $('#editAddressModal').find('#house_no').val(response.address.house_no);
                    $('#editAddressModal').find('#address1').val(response.address.address_line1);
                    $('#editAddressModal').find('#address2').val(response.address.address_line2);
                    $('#editAddressModal').find('#landmark').val(response.address.landmark);
                    $('#editAddressModal').find('#city').val(response.address.city_id);
                    $('#editAddressModal').find('#state').val(response.address.state_id);
                    $('#editAddressModal').find('#zip').val(response.address.zipcode);
                    $('#editAddressModal').find('#country').val(response.address.country_id);
                    $('#editAddressModal').modal('show');
                } else {
                    showToastMessage(response.message, 'error');
                }
            },
            error: function() {
                showToastMessage('An error occurred while removing the item.', 'error');
            }
        });
    }

    function add_to_cart(product_id, customer_id) {
        const csrfToken = $('#csrf-token').val();
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'addToCartFromWishlist']) ?>',
            type: 'POST',
            data: { product_id: product_id, customer_id: customer_id, _csrfToken: csrfToken },
            success: function(response) {
                if (response.success) {
                    showToastMessage(response.message, 'success');
                    window.location.hash = '#wishlist-tab';
                    location.reload();
                } else {
                    showToastMessage(response.message, 'error');
                }
            },
            error: function() {
                showToastMessage('An error occurred while removing the item.', 'error');
            }
        });
    }

    function remove_from_wishlist(product_id, customer_id) {
        const csrfToken = $('#csrf-token').val();
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'removeWishlistData']) ?>',
            type: 'POST',
            data: { product_id: product_id, customer_id: customer_id, _csrfToken: csrfToken },
            success: function(response) {
                if (response.success) {
                    showToastMessage(response.message, 'success');
                    window.location.hash = '#wishlist-tab';
                    location.reload();
                } else {
                    showToastMessage(response.message, 'error');
                }
            },
            error: function() {
                showToastMessage('An error occurred while removing the item.', 'error');
            }
        });
    }

    document.getElementById("profileImagePreview").addEventListener("click", function() {
        document.getElementById("profile_photo").click(); // Trigger form submission
    });

    function previewProfileImage(event) {
        const reader = new FileReader();
        reader.onload = function () {
            document.getElementById('profileImagePreview').src = reader.result;
        };
        reader.readAsDataURL(event.target.files[0]);
    }
    
    function confirmDelete() {
        if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
            document.getElementById('deleteAccountForm').submit();
        }
    }

    $(document).ready(function() {
        // Check for toast message from session
        <?php
        $toastMessage = $session->read('toast_message');
        if ($toastMessage):
            // Clear the message from session after reading
            $session->delete('toast_message');
        ?>
        showToastMessage('<?= h($toastMessage['message']) ?>', '<?= h($toastMessage['type']) ?>');
        <?php endif; ?>
    });

    function showToastMessage(message, type) {
        // Create message container with enhanced styling
        const messageContainer = $(`
            <div class="alert alert-dismissible fade show" style="
                margin-bottom: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                border-radius: 8px;
                border: none;
                animation: slideInRight 0.3s ease-out;
            "></div>
        `);

        // Set message type and styling
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-danger' :
                          type === 'warning' ? 'alert-warning' : 'alert-info';

        messageContainer.addClass(alertClass);

        // Add message content with icon
        const icon = type === 'success' ? 'fas fa-check-circle' :
                    type === 'error' ? 'fas fa-exclamation-circle' :
                    type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';

        messageContainer.html(`
            <div class="alert-body d-flex align-items-center">
                <i class="${icon} me-2"></i>
                <span><b>${message}</b></span>
                <button type="button" class="btn-close ms-auto" onclick="$(this).closest('.alert').fadeOut(300, function(){ $(this).remove(); })"></button>
            </div>
        `);

        // Add to container and show with animation
        $('#toast-message-container').append(messageContainer);

        // Auto hide after 4 seconds
        setTimeout(() => {
            messageContainer.fadeOut(300, function() {
                $(this).remove();
            });
        }, 4000);
    }
</script>
<script src="../../javascript/ozone.js"></script>
    <!-- Bootstrap JS -->
<script src="../../bundles/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"
        integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdn.jsdelivr.net/npm/nouislider@15.7.0/dist/nouislider.min.js"></script>
    <!-- vendors -->
<script src="../../carousel/assets/vendors/highlight.js"></script>
<script src="../../carousel/assets/js/app.js"></script>
<script src="https://cdn.jsdelivr.net/npm/nouislider@15.7.0/dist/nouislider.min.js"></script>
</body>

</html>