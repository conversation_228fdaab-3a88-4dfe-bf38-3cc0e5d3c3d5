<?php

namespace App\Controller\Component;

use Cake\Controller\Component;
use Cake\ORM\TableRegistry;
use Cake\Mailer\Mailer;
use Cake\Core\Configure;
use Cake\Utility\Text;
use Cake\Event\Event;
use Cake\Utility\Security;
use Cake\Auth\DefaultPasswordHasher;
//use QRcode;
use Cake\Http\Session;
use Aws\Sns\SnsClient;
use Aws\Exception\AwsException;
use Twilio\Rest\Client;
use SendGrid\Mail\Mail;
use Cake\View\View;

//require_once(ROOT .DS. "vendor" . DS  . 'qrcode' . DS . 'qrlib.php');
// Testing

class GlobalComponent extends Component
{

    /**
     * <AUTHOR>
     * @Property Common email send function
     *
     **/

    // public function send_email ($to = null, $from = null, $subject = null, $template = null, $viewVars = null, $attachments = null, $cc = null, $layout = null)
    // {

    //     $view_vars = $viewVars ?? [];
    //     $cc = $cc ?? [];

    //     if ($to && $subject) {
    //         $from = $from ?? Configure::read('Settings.FROM_EMAIL');

    //         $Email = new Mailer('default');
    //         // $Email->setTransport('sendgrid');

    //         $Email->setSubject($subject)
    //             ->setEmailFormat('html')
    //             ->setTo($to)
    //             ->setCc($cc)
    //             ->setFrom($from)
    //             ->setViewVars($view_vars);

    //         // echo "<pre>"; print_r($Email); die;
    //         if ($attachments !== null) {
    //             $Email->setAttachments($attachments);
    //         }

    //         $Email->viewBuilder()->setHelpers(['Html', 'Url', 'Text']);

    //         if ($layout !== null) {
    //             $Email->viewBuilder()->setTemplate($template)
    //                 ->setLayout($layout);
    //         } else {
    //             $Email->viewBuilder()->setTemplate($template);
    //         }

    //         try {
    //             $Email->deliver();
    //             \Cake\Log\Log::info('Email Details: ' . print_r($Email, true));
    //             return true;
    //         } catch (\Exception $e) {
    //             \Cake\Log\Log::error('Email sending failed: ' . $e->getMessage());
    //             return false;
    //         }
    //     } else {
    //         return false;
    //     }
    // }

    public function send_email($to = null, $from = null, $subject = null, $template = null, $viewVars = [], $attachments = null, $cc = null, $layout = null)
    {
        $from = $from ?? Configure::read('Settings.FROM_EMAIL');
        $sendgridApiKey = Configure::read('Settings.SENDGRID_API_KEY');

        if (!$to || !$subject || !$template) {
            \Cake\Log\Log::error("Email sending failed: Missing required parameters.");
            return false;
        }

        // Load CakePHP's View for rendering email templates
        $view = new \Cake\View\View();
        $view->set($viewVars);
        $view->disableAutoLayout();

        // Render email content
        $htmlContent = $view->render("email/html/{$template}");
        $textContent = strip_tags($htmlContent); // Generate plain text version if needed

        $email = new \SendGrid\Mail\Mail();
        $email->setFrom($from);
        $email->setSubject($subject);
        // $email->addTo($to);

        // Handle multiple recipients properly
        if (is_array($to)) {
            foreach ($to as $toEmail) {
                if (!empty($toEmail)) {
                    $email->addTo($toEmail);
                }
            }
        } else {
            $email->addTo($to);
        }

        if (!empty($cc)) {
            if (is_array($cc)) {
                foreach ($cc as $ccEmail) {
                    $email->addCc($ccEmail);
                }
            } else {
                $email->addCc($cc);
            }
        }

        // Add HTML content (text/plain is optional)
        $email->addContent("text/html", $htmlContent);

        // Attachments (if any)
        if (!empty($attachments)) {
            foreach ($attachments as $file) {
                $filePath = $file['path'];
                $fileName = $file['name'];
                $fileType = mime_content_type($filePath);
                $email->addAttachment(
                    base64_encode(file_get_contents($filePath)),
                    $fileType,
                    $fileName
                );
            }
        }

        $sendgrid = new \SendGrid($sendgridApiKey);

        try {

            $response = $sendgrid->send($email);

            \Cake\Log\Log::info('Email sent successfully: ' . print_r($response, true));
            return [
                'status' => $response->statusCode(),
                'headers' => $response->headers(),
                'body' => $response->body(),
            ];
        } catch (Exception $e) {

            \Cake\Log\Log::error('Email sending failed: ' . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }

    public function sendOtp($to, $otp)
    {

        $accountSid = Configure::read ('Settings.TWILLIO_SID');
        $authToken = Configure::read ('Settings.TWILLIO_TOKEN');
        $twilioNumber = Configure::read ('Settings.TWILLIO_NUMBER');

        // Initialize the Twilio client
        $client = new Client($accountSid, $authToken);

        // Add a '+' sign if not present
        if (!str_starts_with($to, '+')) {
            $to = '+' . $to;
        }

        try {
            $message = $client->messages->create(
                $to,
                [
                    'from' => $twilioNumber,
                    'body' => "Your babiken OTP is: $otp"
                ]
            );

            return [
                'success' => true,
                'messageId' => $message->sid,
                'message' => 'OTP sent successfully.'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'The OTP could not be sent.'
            ];
        }
    }


    // public function sendOtp($to, $otp)
    // {

    //     $accountSid = Configure::read ('Settings.TWILLIO_SID');
    //     $authToken = Configure::read ('Settings.TWILLIO_TOKEN');
    //     $twilioNumber = Configure::read ('Settings.TWILLIO_NUMBER');
    //     $serviceSid = Configure::read ('Settings.TWILLIO_SSID');

    //     $client = new Client($accountSid, $authToken);

    //     try {

    //         $message = $client->verify->v2->services($serviceSid)
    //             ->verifications
    //             ->create($to, 'sms');

    //         return [
    //             'success' => true,
    //             'messageId' => $message->sid,
    //             'message' => 'OTP sent successfully.'
    //         ];

    //     } catch (\Exception $e) {

    //         // echo "<pre>"; print_r($e->getMessage()); die;
    //         return [
    //             'success' => false,
    //             'error' => $e->getMessage(),
    //             'message' => 'The OTP could not be sent.'
    //         ];
    //     }
    // }

    /**
     * <AUTHOR> Jain
     * @Property create random password
     *
     **/
    public function randomPassword()
    {
        $password = "";
        $charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        for ($i = 0; $i < 8; $i++) {
            $random_int = mt_rand();
            $password .= $charset[$random_int % strlen($charset)];
        }
        return $password;
    }

    public function refer_code($limit)
    {
        return substr(base_convert(sha1(uniqid(mt_rand())), 16, 36), 0, $limit);
    }

    public function sendWelcomeMsg($to,$password)
    {

        $accountSid = Configure::read ('Settings.TWILLIO_SID');
        $authToken = Configure::read ('Settings.TWILLIO_TOKEN');
        $twilioNumber = Configure::read ('Settings.TWILLIO_NUMBER');

        // Initialize the Twilio client
        $client = new Client($accountSid, $authToken);

        // Add a '+' sign if not present
        if (!str_starts_with($to, '+')) {
            $to = '+' . $to;
        }

        try {
            $message = $client->messages->create(
                $to,
                [
                    'from' => $twilioNumber,
                    'body' => "Your have signed up to babiken using the mobile number. Please use : $password". "to log in to Babiken."
                ]
            );

            return [
                'success' => true,
                'messageId' => $message->sid,
                'message' => 'Welcome message sent successfully.'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Welcome message could not be sent.'
            ];
        }
    }

    // public function pushNotification ( $data = null, $device_token = null) {

    //     // echo "<pre>";print_r($data);die;

    //     // API access key from Google API’s Console
    //     define('SERVER_KEY', 'AAAA-V1x1xo:APA91bHRru8cUAwd9JQkgnhTGofm_a4kdO5x976RT6RUY2z7fPJF92ernzEJ3EmQptBjGAyFAvpjJ_d4ptNnixVVcoFN6omCn35uo0Wao6GobYiMnQcQj8t6HsbvD3fBZRtYbltJqksc');

    //     $registrationIds = $device_token;

    //     // Prep the bundle
    //     $msg = array(
    //         'experienceId' => '@khushboo9108/drinkeat',
    //         'body'  => $data['message'],
    //         'title' => $data['title'],
    //         'icon'  => 'myicon', /* Default Icon */
    //         'sound' => 'mySound' /* Default sound */
    //     );

    //     $fields = array(
    //         'registration_ids' => $registrationIds,
    //         /* 'experienceId' => '@khushboo9108/drinkeat', */
    //         'notification' => $msg
    //     );

    //     $headers = array(
    //         'Authorization: key=' . SERVER_KEY,
    //         'Content-Type: application/json'
    //     );

    //     // Send Response to Firebase Server
    //     $ch = curl_init();
    //     curl_setopt($ch, CURLOPT_URL, 'https://fcm.googleapis.com/fcm/send');
    //     curl_setopt($ch, CURLOPT_POST, true);
    //     curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    //     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //     curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    //     curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fields));

    //     $result = curl_exec($ch);
    //     curl_close($ch);

    //     // Echo Result of Firebase Server
    //     return $result;

    // }

    public function pushNotification($data = null, $device_tokens = [])
    {
        if (empty($device_tokens) || empty($data)) {
            return false; // Ensure required data exists
        }

        // API access key from Google API’s Console
        $serverKey = env('FCM_SERVER_KEY', 'AIzaSyAvy-vJJ3kpAxI8Ce6lRFpnqYhVEJenReE'); // Move to env or config

        $notification = [
            'title' => $data['title'],
            'body'  => $data['message'],
            'sound' => 'default'
        ];

        $payload = [
            'registration_ids' => $device_tokens,
            'notification' => $notification,
            'data' => $data, // Include additional data
        ];

        $headers = [
            'Authorization: key=' . $serverKey,
            'Content-Type: application/json'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://fcm.googleapis.com/fcm/send');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));

        $result = curl_exec($ch);
        // curl_close($ch);
        if (curl_errno($ch)) {
            echo 'Curl error: ' . curl_error($ch);
        }
        curl_close($ch);
        var_dump($result);
        die();

        return $result;
    }

    //S send watsapp message using Twilio
    public function sendWhatsAppMessage($recipient_number, $message_text) {

        // Twilio Credentials
        $sid = Configure::read ('Settings.TWILLIO_SID');
        $token = Configure::read ('Settings.TWILLIO_TOKEN');
        $twilio_whatsapp_number = "whatsapp:".Configure::read ('Settings.TWILLIO_NUMBER'); // Twilio number       

        $client = new Client($sid, $token);
        try {
            $send_message = $client->messages->create(
                 "whatsapp:".$recipient_number, // Recipient WhatsApp Number
                [
                    "from" => $twilio_whatsapp_number,
                    "body" => $message_text
                ]
            );

            return true; // Message sent successfully
        } catch (\Twilio\Exceptions\RestException $e) {
            return false; // Failed to send message
        }        
    }


}

?>
