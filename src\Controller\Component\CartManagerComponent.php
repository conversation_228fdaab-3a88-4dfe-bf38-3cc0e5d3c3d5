<?php
namespace App\Controller\Component;

use Cake\Controller\Component;
use Cake\Utility\Text;
use Cake\ORM\TableRegistry;

class CartManagerComponent extends Component
{
    protected $controller;
    protected $Carts;
    protected $CartItems;
    protected $Users;


       public function initialize(array $config): void
    {
        parent::initialize($config);
        $this->controller = $this->_registry->getController();
        $this->Carts = TableRegistry::getTableLocator()->get('Carts');
        $this->CartItems = TableRegistry::getTableLocator()->get('CartItems');
        $this->Users = TableRegistry::getTableLocator()->get('Users');
    }

      public function addToCart($productId, $variantId = null, $quantity = 1)
    {
        $session = $this->controller->getRequest()->getSession();
        $identity = $session->read('Auth.User');
        $customerId = null;

        if ($identity) {
            $user = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();

            if ($user && $user->customer) {
                $customerId = $user->customer->id;
            }
        }

        $guestToken = $session->read('GuestToken');

        if (!$customerId && !$guestToken) {
            $guestToken = Text::uuid();
            $session->write('GuestToken', $guestToken);
        }

        $cart = $this->Carts->find()
            ->where($customerId ? ['customer_id' => $customerId] : ['guest_token' => $guestToken])
            ->first();

        if (!$cart) {
            $cart = $this->Carts->newEntity([
                'customer_id' => $customerId,
                'guest_token' => $guestToken,
            ]);
            $this->Carts->save($cart);
        }

        // Check if the item already exists
        $query = $this->CartItems->find()
            ->where([
                'cart_id' => $cart->id,
                'product_id' => $productId
            ]);

        if ($variantId === null) {
            $query->where(['product_variant_id IS' => null]);
        } else {
            $query->where(['product_variant_id' => $variantId]);
        }

        $existingItem = $query->first();

        // Fetch promotion_price
        $Products = TableRegistry::getTableLocator()->get('Products');
        $product = $Products->find()
            ->select(['promotion_price'])
            ->where(['id' => $productId])
            ->first();

        $unitPrice = $product ? $product->promotion_price : 0;

        \Cake\Log\Log::debug('AddToCart - Product ID: ' . $productId . ', Quantity to add: ' . $quantity . ', Unit Price: ' . $unitPrice);

        if ($existingItem) {
            \Cake\Log\Log::debug('Existing item found - Current quantity: ' . $existingItem->quantity . ', Current price: ' . $existingItem->price);
            $existingItem->quantity += $quantity;
            $existingItem->price = $existingItem->quantity * $unitPrice;
            \Cake\Log\Log::debug('Updated existing item - New quantity: ' . $existingItem->quantity . ', New price: ' . $existingItem->price);
        } else {
            \Cake\Log\Log::debug('Creating new cart item');
            $existingItem = $this->CartItems->newEntity([
                'cart_id' => $cart->id,
                'customer_id'=> $customerId,
                'product_id' => $productId,
                'product_variant_id' => $variantId,
                'quantity' => $quantity,
                'price' => $quantity * $unitPrice,
            ]);
            \Cake\Log\Log::debug('New cart item - Quantity: ' . $quantity . ', Price: ' . ($quantity * $unitPrice));
        }

        $saveResult = $this->CartItems->save($existingItem);
        \Cake\Log\Log::debug('Save result: ' . ($saveResult ? 'Success' : 'Failed'));

        if ($saveResult) {
            \Cake\Log\Log::debug('Final saved item - ID: ' . $existingItem->id . ', Quantity: ' . $existingItem->quantity . ', Price: ' . $existingItem->price);
        }

        return $saveResult;
    }
      public function getCartData($customerId = null, $guestToken = null)
    {
        $cartConditions = $customerId ? ['customer_id' => $customerId] : ['guest_token' => $guestToken];


        $cart = $this->Carts->find()
            ->where($cartConditions)
            ->contain(['CartItems' => ['Products', 'ProductVariants']])
            ->first();


        $cartItems = [];
        $cartOutItems = [];
        $totalPrice = 0;
        $totalSalePrice = 0;

        if ($cart) {
            \Cake\Log\Log::debug('Cart found with ID: ' . $cart->id . ', Total cart items: ' . count($cart->cart_items));

            foreach ($cart->cart_items as $item) {
                \Cake\Log\Log::debug('Processing cart item - ID: ' . $item->id . ', Product ID: ' . $item->product_id . ', Quantity: ' . $item->quantity . ', Price: ' . $item->price);

                $price = null;
                $salePrice = null;

                // if ($item->product_variant_id && $item->product_variant) {
                //     $price = $item->product_variant->promotion_price;
                //     $salePrice = $item->product_variant->sales_price;
                // }

                if (!$price) {
                    $product = $item->product;
                    $price = $product->promotion_price ?? $this->controller->Products->getProductPrice($item->product_id);
                    $salePrice = $product->sales_price ?? null;
                }

                \Cake\Log\Log::debug('Product prices - Unit price: ' . $price . ', Sale price: ' . $salePrice);

                // $image = $this->controller->ProductImages->getDefaultProductImage($item->product_id);
                // $availability = $this->controller->Products->getAvailabilityStatus($item->product_id);

                // if ($image) {
                //     $image = $this->controller->Media->getCloudFrontURL($image);
                // }

                // $discount = $this->controller->Products->getDiscountProduct($item->product_id, $item->product_variant_id);
                $unitSalePrice = $salePrice ?? $item->product->sales_price;

                $totalRowPrice = $item->quantity * $price;
                $totalRowSalePrice = $item->quantity * $unitSalePrice;

                $totalPrice += $totalRowPrice;
                $totalSalePrice += $totalRowSalePrice;

                $itemData = [
                    'cart_item_id' => $item->id,
                    'product_id' => $item->product_id,
                    'product_variant_id' => $item->product_variant_id,
                    'product_name' => $item->product->name,
                    'variant_name' => $item->product_variant->name ?? $item->product->name,
                    'quantity' => $item->quantity,
                    'price' => $price,
                    'sale_price' => number_format($totalRowSalePrice, 2),
                    'total_price' => number_format($totalRowPrice, 2),
                   // 'discount' => $discount,
                    // 'product_image' => $image,
                    // 'get_available_status' => $availability
                ];

                \Cake\Log\Log::debug('Item data for template: ' . json_encode($itemData));
                $cartItems[] = $itemData;
                // if ($availability === "In Stock") {
                //     $cartItems[] = $itemData;
                // } else {
                //     $cartOutItems[] = $itemData;
                // }
            }
        } else {
            \Cake\Log\Log::debug('No cart found for customer ID: ' . $customerId . ', guest token: ' . $guestToken);
        }

        return [
            'cart_id' => $cart->id ?? 0,
            'cartItems' => $cartItems,
            'cartOutItems' => $cartOutItems,
            'totalPrice' => number_format($totalPrice, 2),
            'totalSalePrice' => number_format($totalSalePrice, 2),
            // 'totalDiscountedPrice' => number_format($totalSalePrice - $totalPrice, 2),
            'total_items' => count($cartItems),
            'checkCartCount' => count($cartItems) + count($cartOutItems)
        ];
    }

    public function getOrderSummary($customerId = null, $guestToken = null, $region = 'Qatar', $shippingCost = 50.00, $discountAmount = 0.00, $couponCode = null)
    {
        // Get cart data
        $cartData = $this->getCartData($customerId, $guestToken);

        // Calculate subtotal (remove formatting to get numeric value)
        $subtotal = (float) str_replace(',', '', $cartData['totalPrice']);

        // Calculate total savings (difference between sale price and promotion price)
        $totalSalePrice = (float) str_replace(',', '', $cartData['totalSalePrice']);
        $totalSavings = $totalSalePrice - $subtotal;

        // Calculate final total
        $finalTotal = $subtotal - $discountAmount + $shippingCost;

        // Estimated delivery date (3-5 business days from now)
        $estimatedDeliveryDate = date('d M, Y', strtotime('+4 days'));

        return [
            'cart_id' => $cartData['cart_id'],
            'cart_items' => $cartData['cartItems'],
            'total_items' => $cartData['total_items'],
            'subtotal' => $subtotal,
            'subtotal_formatted' => number_format($subtotal, 2) . ' QAR',
            'discount_amount' => $discountAmount,
            'discount_formatted' => number_format($discountAmount, 2) . ' QAR',
            'total_savings' => $totalSavings,
            'total_savings_formatted' => number_format($totalSavings, 2) . ' QAR',
            'region' => $region,
            'shipping_cost' => $shippingCost,
            'shipping_cost_formatted' => number_format($shippingCost, 2) . ' QAR',
            'final_total' => $finalTotal,
            'final_total_formatted' => number_format($finalTotal, 2) . ' QAR',
            'estimated_delivery_date' => $estimatedDeliveryDate,
            'coupon_code' => $couponCode,
            'has_items' => $cartData['total_items'] > 0
        ];
    }


    public function applyCoupon($couponCode, $subtotal)
    {

        $discountAmount = 0;
        $isValid = false;
        $message = 'Invalid coupon code';


        $validCoupons = [
            'SAVE10' => ['type' => 'percentage', 'value' => 10, 'min_amount' => 100],
            'FLAT50' => ['type' => 'fixed', 'value' => 50, 'min_amount' => 200],
            'WELCOME20' => ['type' => 'percentage', 'value' => 20, 'min_amount' => 150]
        ];

        if (isset($validCoupons[$couponCode])) {
            $coupon = $validCoupons[$couponCode];

            if ($subtotal >= $coupon['min_amount']) {
                $isValid = true;

                if ($coupon['type'] === 'percentage') {
                    $discountAmount = ($subtotal * $coupon['value']) / 100;
                    $message = "Coupon applied! {$coupon['value']}% discount";
                } else {
                    $discountAmount = $coupon['value'];
                    $message = "Coupon applied! {$coupon['value']} QAR discount";
                }
            } else {
                $message = "Minimum order amount of {$coupon['min_amount']} QAR required";
            }
        }

        return [
            'is_valid' => $isValid,
            'discount_amount' => $discountAmount,
            'message' => $message,
            'coupon_code' => $couponCode
        ];
    }


    public function removeCoupon($couponCode)
    {
        return [
            'is_removed' => true,
            'message' => "Coupon '{$couponCode}' has been removed successfully",
            'coupon_code' => $couponCode
        ];
    }


}